/**
 * NodePropertiesPanel - 节点属性面板组件
 * 提供节点属性的查看和编辑功能
 */

'use client';

import React, { useCallback, useState } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import { GameNodeType } from '../../../../src/types/NodeTypes';
import { fbxModelLoader } from '../../../../src/three/loaders/FBXModelLoader';

interface NodePropertiesPanelProps {
  className?: string;
}

export const NodePropertiesPanel: React.FC<NodePropertiesPanelProps> = ({ className = '' }) => {
  const {
    nodes,
    selectedNode,
    setSelectedNode,
    setNodes,
    nodeStats,
    tempInputValues,
    setTempInputValues,
    isUploadingModel,
    uploadError,
    isDragOver,
    setIsUploadingModel,
    setUploadError,
    setIsDragOver,
    threeContext
  } = useNodeSystem();

  const [editingScriptId, setEditingScriptId] = useState<string | null>(null);
  const [editingScriptName, setEditingScriptName] = useState<string>('');
  const [editingNodeName, setEditingNodeName] = useState<string>('');
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false);

  // 动画和材质上传状态
  const [isUploadingAnimation, setIsUploadingAnimation] = useState<boolean>(false);
  const [isUploadingMaterial, setIsUploadingMaterial] = useState<boolean>(false);
  const [animationError, setAnimationError] = useState<string | null>(null);
  const [materialError, setMaterialError] = useState<string | null>(null);
  const [isDragOverAnimation, setIsDragOverAnimation] = useState<boolean>(false);
  const [isDragOverMaterial, setIsDragOverMaterial] = useState<boolean>(false);

  // 动画混合器引用
  const [animationMixer, setAnimationMixer] = useState<THREE.AnimationMixer | null>(null);

  // 应用模型到节点
  const applyModelToNode = useCallback(async (nodeId: string, modelPath: string, fileExtension: string) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[模型应用] 未找到目标节点:', nodeId);
      return;
    }

    try {
      let newModel: THREE.Object3D;

      if (fileExtension === '.fbx') {
        // 使用FBX加载器
        const fbxModel = await fbxModelLoader.loadModel({
          url: modelPath,
          autoPlayAnimation: true,
          enableShadows: true,
          scale: 1, // 先使用原始大小，后续可调整
          position: existingObject.position.clone(),
          rotation: existingObject.rotation.clone()
        });
        newModel = fbxModel.object;

        console.log('[模型应用] FBX模型信息:', {
          position: newModel.position,
          scale: newModel.scale,
          boundingBox: new THREE.Box3().setFromObject(newModel)
        });
      } else {
        // 使用GLTF加载器
        const gltfLoader = new GLTFLoader();
        const gltf = await new Promise<any>((resolve, reject) => {
          gltfLoader.load(
            modelPath,
            (gltf) => resolve(gltf),
            undefined,
            (error) => reject(error)
          );
        });

        newModel = gltf.scene;
        newModel.position.copy(existingObject.position);
        newModel.rotation.copy(existingObject.rotation);
        newModel.scale.copy(existingObject.scale);

        console.log('[模型应用] GLTF模型信息:', {
          position: newModel.position,
          scale: newModel.scale,
          boundingBox: new THREE.Box3().setFromObject(newModel)
        });
      }

      // 设置模型名称为节点ID，以便后续查找
      newModel.name = nodeId;

      // 移除旧对象，添加新模型
      console.log('[模型应用] 移除旧对象:', existingObject.name);
      scene.remove(existingObject);

      console.log('[模型应用] 添加新模型到场景:', {
        name: newModel.name,
        position: newModel.position,
        visible: newModel.visible,
        children: newModel.children.length
      });
      scene.add(newModel);

      // 验证模型是否在场景中
      const addedModel = scene.getObjectByName(nodeId);
      console.log('[模型应用] 验证模型是否在场景中:', !!addedModel);

      console.log('[模型应用] 模型已成功应用到节点:', nodeId);

    } catch (error) {
      console.error('[模型应用] 模型应用失败:', error);
      throw new Error('模型加载失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [threeContext]);

  // 更新节点属性
  const updateNodeProperty = useCallback((property: string, value: string) => {
    if (!selectedNode || !threeContext) return;

    const { scene } = threeContext;
    const object = scene.getObjectByName(selectedNode.id);

    if (!object) return;

    if (property === 'name') {
      // 更新节点名称
      console.log('[NodePropertiesPanel] 更新节点名称:', selectedNode.id, '->', value);

      // 更新当前选中节点的名称
      const updatedSelectedNode = { ...selectedNode, name: value };
      setSelectedNode(updatedSelectedNode);

      // 更新节点列表中对应节点的名称
      setNodes(nodes.map(node =>
        node.id === selectedNode.id
          ? { ...node, name: value }
          : node
      ));

      console.log('[NodePropertiesPanel] 节点名称已更新');
      setEditingNodeName(''); // 清空编辑状态
    } else if (property.startsWith('position.')) {
      const axis = property.split('.')[1] as 'x' | 'y' | 'z';
      const tempKey = `position${axis.toUpperCase()}` as keyof typeof tempInputValues;
      
      const isValidPartialNumber = /^-?(\d+\.?\d*|\.\d*|\.)$/.test(value) || value === '' || value === '-';
      
      if (isValidPartialNumber) {
        setTempInputValues({ [tempKey]: value });
        
        const numericValue = parseFloat(value);
        if (!isNaN(numericValue)) {
          object.position[axis] = numericValue;
        }
      }
    } else if (property.startsWith('rotation.')) {
      const axis = property.split('.')[1] as 'x' | 'y' | 'z';
      const tempKey = `rotation${axis.toUpperCase()}` as keyof typeof tempInputValues;
      
      const isValidPartialNumber = /^-?(\d+\.?\d*|\.\d*|\.)$/.test(value) || value === '' || value === '-';
      
      if (isValidPartialNumber) {
        setTempInputValues({ [tempKey]: value });
        
        const numericValue = parseFloat(value);
        if (!isNaN(numericValue)) {
          object.rotation[axis] = (numericValue * Math.PI) / 180; // 转换为弧度
        }
      }
    } else if (property.startsWith('scale.')) {
      const axis = property.split('.')[1] as 'x' | 'y' | 'z';
      const tempKey = `scale${axis.toUpperCase()}` as keyof typeof tempInputValues;
      
      const isValidPartialNumber = /^(\d+\.?\d*|\.\d*|\.)$/.test(value) || value === '';
      
      if (isValidPartialNumber) {
        setTempInputValues({ [tempKey]: value });
        
        const numericValue = parseFloat(value);
        if (!isNaN(numericValue) && numericValue > 0) {
          object.scale[axis] = numericValue;
        }
      }
    }
  }, [selectedNode, threeContext, tempInputValues, setTempInputValues]);

  // 保存节点属性
  const saveNodeProperties = useCallback(async (node: typeof selectedNode) => {
    if (!node || !threeContext) return;

    try {
      const { scene } = threeContext;
      const object = scene.getObjectByName(node.id);

      if (object) {
        // 获取Three.js对象的当前状态
        const nodeProperty = {
          id: node.id,
          name: node.name, // 直接使用节点的当前名称，因为已经通过updateNodeProperty更新了
          type: node.type,
          position: {
            x: object.position.x,
            y: object.position.y,
            z: object.position.z
          },
          rotation: {
            x: object.rotation.x,
            y: object.rotation.y,
            z: object.rotation.z
          },
          scaling: {
            x: object.scale.x,
            y: object.scale.y,
            z: object.scale.z
          },
          color: ('material' in node && node.material?.diffuseColor) ? {
            r: node.material.diffuseColor.r,
            g: node.material.diffuseColor.g,
            b: node.material.diffuseColor.b
          } : undefined
        };

        // 调用API保存到文件
        const response = await fetch('/api/node-properties', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ nodeProperty })
        });

        const result = await response.json();

        if (result.success) {
          console.log('[节点属性] 保存成功:', result.message);

          // 清空临时编辑状态
          setTempInputValues({});
          setEditingNodeName('');

          // 显示成功提示
          setSaveSuccess(true);
          setTimeout(() => setSaveSuccess(false), 3000); // 3秒后隐藏提示
        } else {
          throw new Error(result.error || '保存失败');
        }
      }

    } catch (error) {
      console.error('[节点属性] 保存时发生错误:', error);
    }
  }, [threeContext, editingNodeName, tempInputValues, setTempInputValues]);

  // 处理模型上传
  const handleModelUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingModel(true);
    setUploadError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.fbx', '.glb', '.gltf'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        throw new Error('不支持的文件格式。请上传 .fbx、.glb 或 .gltf 文件。');
      }

      console.log('[模型上传] 开始上传:', file.name, 'to node:', nodeId);

      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);

      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await uploadResponse.json();

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '文件上传失败');
      }

      console.log('[模型上传] 文件上传成功:', uploadResult.data.filePath);

      // 2. 加载并应用模型到Three.js场景
      if (threeContext) {
        await applyModelToNode(nodeId, uploadResult.data.filePath, fileExtension);
      }

      console.log('[模型上传] 模型应用成功');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      setUploadError(errorMessage);
      console.error('[模型上传] 上传失败:', error);
    } finally {
      setIsUploadingModel(false);
    }
  }, [setIsUploadingModel, setUploadError, threeContext, applyModelToNode]);

  // 处理动画文件上传
  const handleAnimationUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingAnimation(true);
    setAnimationError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.fbx'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        throw new Error('动画文件仅支持 .fbx 格式');
      }

      console.log('[动画上传] 开始上传动画文件:', file.name, 'to node:', nodeId);

      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);
      formData.append('type', 'animation');

      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await uploadResponse.json();

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '动画文件上传失败');
      }

      console.log('[动画上传] 动画文件上传成功:', uploadResult.data.filePath);

      // 2. 加载并应用动画到现有模型
      if (threeContext) {
        await applyAnimationToNode(nodeId, uploadResult.data.filePath);
      }

      console.log('[动画上传] 动画应用成功');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '动画上传失败';
      setAnimationError(errorMessage);
      console.error('[动画上传] 动画上传失败:', error);
    } finally {
      setIsUploadingAnimation(false);
    }
  }, [setIsUploadingAnimation, setAnimationError, threeContext]);

  // 应用动画到节点
  const applyAnimationToNode = useCallback(async (nodeId: string, animationPath: string) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[动画应用] 未找到目标节点:', nodeId);
      return;
    }

    try {
      console.log('[动画应用] 开始加载动画文件:', animationPath);

      // 使用FBXLoader加载动画文件
      const fbxLoader = new FBXLoader();
      const animationFBX = await new Promise<THREE.Group>((resolve, reject) => {
        fbxLoader.load(
          animationPath,
          (object: THREE.Group) => resolve(object),
          undefined,
          (error: any) => reject(error)
        );
      });

      console.log('[动画应用] 动画文件加载成功，动画数量:', animationFBX.animations.length);

      if (animationFBX.animations.length === 0) {
        throw new Error('动画文件中未找到动画数据');
      }

      // 创建或更新AnimationMixer
      let mixer = existingObject.userData.animationMixer as THREE.AnimationMixer;
      if (!mixer) {
        mixer = new THREE.AnimationMixer(existingObject);
        existingObject.userData.animationMixer = mixer;
        setAnimationMixer(mixer);
        console.log('[动画应用] 创建新的AnimationMixer并存储到对象userData');
      }

      // 停止所有现有动画
      mixer.stopAllAction();

      // 添加新动画并播放
      animationFBX.animations.forEach((clip, index) => {
        console.log(`[动画应用] 添加动画 ${index + 1}: ${clip.name}, 时长: ${clip.duration}秒`);
        const action = mixer.clipAction(clip);
        action.play();
      });

      console.log('[动画应用] 动画已成功应用到节点:', nodeId);

    } catch (error) {
      console.error('[动画应用] 动画应用失败:', error);
      throw new Error('动画加载失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [threeContext, setAnimationMixer]);

  // 应用材质到节点
  const applyMaterialToNode = useCallback(async (nodeId: string, materialPath: string, fileExtension: string) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[材质应用] 未找到目标节点:', nodeId);
      return;
    }

    try {
      console.log('[材质应用] 开始加载材质文件:', materialPath);

      let newMaterial: THREE.Material;

      if (fileExtension === '.json') {
        // 加载JSON材质定义
        const response = await fetch(materialPath);
        const materialData = await response.json();

        // 根据材质类型创建材质
        switch (materialData.type) {
          case 'MeshStandardMaterial':
            newMaterial = new THREE.MeshStandardMaterial(materialData);
            break;
          case 'MeshPhongMaterial':
            newMaterial = new THREE.MeshPhongMaterial(materialData);
            break;
          case 'MeshBasicMaterial':
            newMaterial = new THREE.MeshBasicMaterial(materialData);
            break;
          default:
            newMaterial = new THREE.MeshStandardMaterial(materialData);
        }

        console.log('[材质应用] JSON材质加载成功:', materialData.type);
      } else {
        // 加载纹理图片
        const textureLoader = new THREE.TextureLoader();
        const texture = await new Promise<THREE.Texture>((resolve, reject) => {
          textureLoader.load(
            materialPath,
            (texture: THREE.Texture) => {
              texture.colorSpace = THREE.SRGBColorSpace;
              resolve(texture);
            },
            undefined,
            (error: unknown) => reject(error)
          );
        });

        // 创建带纹理的材质
        newMaterial = new THREE.MeshStandardMaterial({
          map: texture,
          side: THREE.DoubleSide
        });

        console.log('[材质应用] 纹理材质加载成功');
      }

      // 应用材质到对象
      existingObject.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.material = newMaterial;
          console.log(`[材质应用] 材质已应用到网格: ${child.name || 'unnamed'}`);
        }
      });

      console.log('[材质应用] 材质已成功应用到节点:', nodeId);

    } catch (error) {
      console.error('[材质应用] 材质应用失败:', error);
      throw new Error('材质加载失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [threeContext]);

  // 处理材质文件上传
  const handleMaterialUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingMaterial(true);
    setMaterialError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tga', '.json'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        throw new Error('材质文件支持格式: .jpg, .jpeg, .png, .bmp, .tga, .json');
      }

      console.log('[材质上传] 开始上传材质文件:', file.name, 'to node:', nodeId);

      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);
      formData.append('type', 'material');

      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await uploadResponse.json();

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '材质文件上传失败');
      }

      console.log('[材质上传] 材质文件上传成功:', uploadResult.data.filePath);

      // 2. 加载并应用材质到现有模型
      if (threeContext) {
        await applyMaterialToNode(nodeId, uploadResult.data.filePath, fileExtension);
      }

      console.log('[材质上传] 材质应用成功');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '材质上传失败';
      setMaterialError(errorMessage);
      console.error('[材质上传] 材质上传失败:', error);
    } finally {
      setIsUploadingMaterial(false);
    }
  }, [setIsUploadingMaterial, setMaterialError, threeContext, applyMaterialToNode]);

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, [setIsDragOver]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, [setIsDragOver]);

  const handleDrop = useCallback((e: React.DragEvent, nodeId: string) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const modelFile = files.find(file => {
      const extension = '.' + file.name.split('.').pop()?.toLowerCase();
      return ['.fbx', '.glb', '.gltf'].includes(extension);
    });

    if (modelFile) {
      handleModelUpload(modelFile, nodeId);
    } else {
      setUploadError('请拖拽 .fbx、.glb 或 .gltf 模型文件');
    }
  }, [handleModelUpload, setIsDragOver, setUploadError]);

  if (!selectedNode) {
    return (
      <div className={`w-80 bg-white border-l border-gray-200 flex flex-col ${className}`}>
        {/* 标题 */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-bold text-gray-900">🎛️ 节点系统</h3>
          <p className="text-sm text-gray-600 mt-1">
            总计: {nodeStats.total} 个节点
          </p>
        </div>

        {/* 节点列表 */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">节点列表</h4>

            {nodes.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p className="text-sm">暂无节点</p>
                <p className="text-xs mt-1">点击"添加方块"创建节点</p>
              </div>
            ) : (
              <div className="space-y-2">
                {nodes.map((node) => (
                  <div
                    key={node.id}
                    onClick={() => setSelectedNode(node)}
                    className="p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {node.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {node.type}
                        </p>
                      </div>
                      <div className="ml-2 flex-shrink-0">
                        <div className={`w-3 h-3 rounded-full ${
                          node.visible ? 'bg-green-400' : 'bg-gray-400'
                        }`} />
                      </div>
                    </div>

                    {/* 位置信息 */}
                    <div className="mt-2 text-xs text-gray-600">
                      {node.position ? (
                        `位置: (${node.position.x?.toFixed(1) ?? '0'}, ${node.position.y?.toFixed(1) ?? '0'}, ${node.position.z?.toFixed(1) ?? '0'})`
                      ) : (
                        '位置: 未设置'
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 节点统计 */}
          {Object.keys(nodeStats.byType).length > 0 && (
            <div className="px-4 pb-4">
              <h5 className="text-xs font-medium text-gray-700 mb-2">类型统计</h5>
              <div className="space-y-1">
                {Object.entries(nodeStats.byType).map(([type, count]) => (
                  <div key={type} className="flex justify-between text-xs">
                    <span className="text-gray-600">{type}</span>
                    <span className="text-gray-900 font-medium">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`w-80 bg-white border-l border-gray-200 flex flex-col ${className}`}>
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-bold text-gray-900">🎛️ 节点属性</h3>
            <p className="text-sm text-gray-600">编辑选中节点的属性</p>
          </div>
          <button
            onClick={() => setSelectedNode(null)}
            className="text-gray-500 hover:text-gray-700 p-1 rounded transition-colors"
            title="返回节点列表"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* 节点名称 */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-900">名称</label>
          <input
            type="text"
            value={editingNodeName || selectedNode.name}
            onChange={(e) => setEditingNodeName(e.target.value)}
            onBlur={() => {
              if (editingNodeName && editingNodeName !== selectedNode.name) {
                updateNodeProperty('name', editingNodeName);
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.currentTarget.blur();
              }
            }}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* 位置属性 */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-900">位置 (Position)</label>
          <div className="grid grid-cols-3 gap-2">
            {(['x', 'y', 'z'] as const).map((axis) => (
              <div key={axis}>
                <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
                <input
                  type="text"
                  value={tempInputValues[`position${axis.toUpperCase()}` as keyof typeof tempInputValues] ??
                         selectedNode.position?.[axis]?.toFixed(2) ?? '0'}
                  onChange={(e) => updateNodeProperty(`position.${axis}`, e.target.value)}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            ))}
          </div>
        </div>

        {/* 旋转属性 */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-900">旋转 (Rotation)</label>
          <div className="grid grid-cols-3 gap-2">
            {(['x', 'y', 'z'] as const).map((axis) => (
              <div key={axis}>
                <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}°</label>
                <input
                  type="text"
                  value={tempInputValues[`rotation${axis.toUpperCase()}` as keyof typeof tempInputValues] ??
                         ((selectedNode.rotation?.[axis] * 180) / Math.PI)?.toFixed(1) ?? '0'}
                  onChange={(e) => updateNodeProperty(`rotation.${axis}`, e.target.value)}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            ))}
          </div>
        </div>

        {/* 缩放属性 */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-900">缩放 (Scale)</label>
          <div className="grid grid-cols-3 gap-2">
            {(['x', 'y', 'z'] as const).map((axis) => (
              <div key={axis}>
                <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
                <input
                  type="text"
                  value={tempInputValues[`scale${axis.toUpperCase()}` as keyof typeof tempInputValues] ??
                         selectedNode.scaling?.[axis]?.toFixed(2) ?? '1'}
                  onChange={(e) => updateNodeProperty(`scale.${axis}`, e.target.value)}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            ))}
          </div>
        </div>

        {/* 模型上传区域 */}
        {selectedNode.type === GameNodeType.MESH && (
          <div>
            <label className="block text-sm font-medium mb-2 text-gray-900">模型文件</label>
            <div
              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
                isDragOver
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-200'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, selectedNode.id)}
            >
              <input
                type="file"
                accept=".fbx,.glb,.gltf"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleModelUpload(file, selectedNode.id);
                  }
                }}
                className="hidden"
                id="model-upload"
                disabled={isUploadingModel}
              />
              <label
                htmlFor="model-upload"
                className={`flex flex-col items-center justify-center cursor-pointer ${
                  isUploadingModel ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                <div className="w-8 h-8 mb-2 text-gray-600">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                </div>
                {isUploadingModel ? (
                  <div className="flex flex-col items-center">
                    <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                    <p className="text-sm text-gray-600">正在处理模型...</p>
                  </div>
                ) : (
                  <>
                    <p className="text-sm text-gray-700 mb-1">
                      {isDragOver ? '松开以上传模型' : '点击或拖拽上传模型文件'}
                    </p>
                    <p className="text-xs text-gray-600">支持 .fbx、.glb 和 .gltf 格式</p>
                  </>
                )}
              </label>
            </div>
            
            {uploadError && (
              <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-sm text-red-800">
                {uploadError}
              </div>
            )}
          </div>
        )}

        {/* 动画文件上传区域 */}
        {selectedNode.type === GameNodeType.MESH && (
          <div>
            <label className="block text-sm font-medium mb-2 text-gray-900">动画文件</label>
            <div
              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
                isDragOverAnimation
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-300 hover:border-gray-200'
              }`}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragOverAnimation(true);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                setIsDragOverAnimation(false);
              }}
              onDrop={(e) => {
                e.preventDefault();
                setIsDragOverAnimation(false);
                const files = Array.from(e.dataTransfer.files);
                const file = files[0];
                if (file) {
                  handleAnimationUpload(file, selectedNode.id);
                }
              }}
            >
              <input
                type="file"
                accept=".fbx"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleAnimationUpload(file, selectedNode.id);
                  }
                }}
                className="hidden"
                id="animation-upload"
                disabled={isUploadingAnimation}
              />
              <label
                htmlFor="animation-upload"
                className={`flex flex-col items-center justify-center cursor-pointer ${
                  isUploadingAnimation ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                <div className="w-8 h-8 mb-2 text-green-600">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
                  </svg>
                </div>
                {isUploadingAnimation ? (
                  <div className="flex flex-col items-center">
                    <div className="w-6 h-6 border-2 border-green-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                    <p className="text-sm text-gray-600">正在处理动画...</p>
                  </div>
                ) : (
                  <>
                    <p className="text-sm text-gray-700 mb-1">
                      {isDragOverAnimation ? '松开以上传动画' : '点击或拖拽上传动画文件'}
                    </p>
                    <p className="text-xs text-gray-600">支持 .fbx 格式</p>
                  </>
                )}
              </label>
            </div>

            {animationError && (
              <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-sm text-red-800">
                {animationError}
              </div>
            )}
          </div>
        )}

        {/* 材质文件上传区域 */}
        {selectedNode.type === GameNodeType.MESH && (
          <div>
            <label className="block text-sm font-medium mb-2 text-gray-900">材质文件</label>
            <div
              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
                isDragOverMaterial
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-300 hover:border-gray-200'
              }`}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragOverMaterial(true);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                setIsDragOverMaterial(false);
              }}
              onDrop={(e) => {
                e.preventDefault();
                setIsDragOverMaterial(false);
                const files = Array.from(e.dataTransfer.files);
                const file = files[0];
                if (file) {
                  handleMaterialUpload(file, selectedNode.id);
                }
              }}
            >
              <input
                type="file"
                accept=".jpg,.jpeg,.png,.bmp,.tga,.json"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleMaterialUpload(file, selectedNode.id);
                  }
                }}
                className="hidden"
                id="material-upload"
                disabled={isUploadingMaterial}
              />
              <label
                htmlFor="material-upload"
                className={`flex flex-col items-center justify-center cursor-pointer ${
                  isUploadingMaterial ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                <div className="w-8 h-8 mb-2 text-purple-600">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                  </svg>
                </div>
                {isUploadingMaterial ? (
                  <div className="flex flex-col items-center">
                    <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                    <p className="text-sm text-gray-600">正在处理材质...</p>
                  </div>
                ) : (
                  <>
                    <p className="text-sm text-gray-700 mb-1">
                      {isDragOverMaterial ? '松开以上传材质' : '点击或拖拽上传材质文件'}
                    </p>
                    <p className="text-xs text-gray-600">支持图片和 .json 材质定义</p>
                  </>
                )}
              </label>
            </div>

            {materialError && (
              <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-sm text-red-800">
                {materialError}
              </div>
            )}
          </div>
        )}

        {/* 分隔线 */}
        <hr className="border-gray-300" />

        {/* 节点信息 */}
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-900">类型</label>
            <p className="text-sm text-gray-700 bg-gray-100 rounded px-3 py-2">
              {selectedNode.type}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 text-gray-900">ID</label>
            <p className="text-xs text-gray-600 bg-gray-100 rounded px-3 py-2 font-mono break-all">
              {selectedNode.id}
            </p>
          </div>
        </div>

        {/* 成功提示 */}
        {saveSuccess && (
          <div className="p-3 bg-green-100 border border-green-300 rounded-lg text-sm text-green-800">
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              节点属性保存成功！
            </div>
          </div>
        )}

        {/* 保存按钮 */}
        <div className="pt-4">
          <button
            onClick={() => saveNodeProperties(selectedNode)}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
          >
            💾 保存属性
          </button>
        </div>
      </div>
    </div>
  );
};

export default NodePropertiesPanel;
